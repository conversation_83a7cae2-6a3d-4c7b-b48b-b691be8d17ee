import { useRef, useState, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import { ParticipantName, TrackMutedIndicator, ConnectionQualityIndicator } from '@livekit/components-react';
import { generateAvatar, parseMetadata } from '../utils/helper';

// Responsive Grid PiP Content Component
function SimplePipContent({ localParticipant }) {
  // Get avatar initials
  const avatarName = localParticipant?.name
    ? generateAvatar(localParticipant.name)
    : generateAvatar(localParticipant.identity);
  // Get avatar color from metadata or fallback
  let avatarColor = '#7C4DFF';
  try {
    const metaColor = parseMetadata(localParticipant?.metadata)?.color;
    if (metaColor) avatarColor = metaColor;
  } catch (e) { /* ignore */ }

  return (
    <div className="pip-grid-container">
      {/* Main large tile */}
      <div className="pip-tile pip-tile-main pip-tile-pip-custom">
        <div className="pip-tile-participant-wrapper">
          {/* Centered, responsive avatar */}
          <div className="pip-tile-avatar-center">
            <div
              className="pip-tile-avatar"
              style={{ backgroundColor: avatarColor }}
            >
              {avatarName}
            </div>
          </div>
          {/* Custom overlay (mic, name, network) */}
          <div className="pip-tile-overlay">
            <div className="pip-tile-overlay-left">
              <TrackMutedIndicator
                trackRef={{ participant: localParticipant, source: Track.Source.Microphone }}
                show={"muted"}
              />
              <ParticipantName participant={localParticipant} />
            </div>
            <div className="pip-tile-overlay-right">
              <ConnectionQualityIndicator participant={localParticipant} />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom row - 3 smaller tiles */}
      <div className="pip-tile pip-tile-small pip-tile-1">
        <div className="pip-tile-center-dot" />
      </div>
      <div className="pip-tile pip-tile-small pip-tile-2">
        <div className="pip-tile-center-dot" />
      </div>
      <div className="pip-tile pip-tile-small pip-tile-3">
        <div className="pip-tile-center-dot" />
      </div>
    </div>
  );
}

export function usePictureInPicture({
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  localParticipant
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Simple configuration
  const defaultConfig = useMemo(() => ({
    width: 320,
    height: 240
  }), []);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Simple PiP Content
  const PipContent = useCallback(() => {
    return <SimplePipContent localParticipant={localParticipant} />;
  }, [localParticipant]);

  // Responsive Grid styles
  const getPipStyles = useCallback(() => `
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      background: #333;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      color: white;
    }

    .pip-container {
      width: 100vw;
      height: 100vh;
      background: #333;
      padding: 2vmin;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
    }

    .pip-grid-container {
      width: 100%;
      height: 100%;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-template-rows: 2fr 1fr;
      gap: 1vmin;
      box-sizing: border-box;
    }

    .pip-tile {
      border: 0.5vmin solid #2196F3;
      border-radius: 1vmin;
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
    }

    .pip-tile-main {
      grid-column: 1 / -1;
      grid-row: 1;
      background-color: #e8e8e8;
    }

    .pip-tile-small {
      grid-row: 2;
      aspect-ratio: 16 / 9;
    }

    .pip-tile-1 {
      background-color: #d4d4d4;
    }

    .pip-tile-2 {
      background-color: #c0c0c0;
    }

    .pip-tile-3 {
      background-color: #b0b0b0;
    }

    .pip-tile-center-dot {
      width: 2vmin;
      height: 2vmin;
      background-color: #ff69b4;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .pip-tile-pip-custom .lk-participant-metadata {
      display: none !important;
    }
    .pip-tile-participant-wrapper {
      width: 100%;
      height: 100%;
      position: relative;
    }
    .pip-tile-overlay {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      pointer-events: none;
      padding: 0.25em 0.4em 0.35em 0.4em;
      box-sizing: border-box;
    }
    .pip-tile-overlay-left {
      display: flex;
      align-items: center;
      gap: 0.35em;
      background: rgba(20,20,20,0.72);
      border-radius: 0.35em;
      padding: 0.12em 0.5em 0.12em 0.3em;
      font-size: 0.85em;
      color: #fff;
      pointer-events: auto;
      min-height: 1.7em;
    }
    .pip-tile-overlay-left svg {
      width: 1.1em;
      height: 1.1em;
      margin-right: 0.1em;
    }
    .pip-tile-overlay-right {
      display: flex;
      align-items: center;
      background: rgba(20,20,20,0.72);
      border-radius: 0.35em;
      padding: 0.12em 0.4em;
      font-size: 0.85em;
      color: #fff;
      pointer-events: auto;
      min-height: 1.7em;
    }
    .pip-tile-overlay-right svg {
      width: 1.1em;
      height: 1.1em;
    }
    .pip-tile-avatar-center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      pointer-events: none;
    }
    .pip-tile-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      color: #fff;
      font-weight: 600;
      font-size: 6vmin;
      background: #7C4DFF;
      box-shadow: 0 2px 12px rgba(0,0,0,0.12);
      user-select: none;
      pointer-events: auto;
      transition: width 0.2s, height 0.2s, font-size 0.2s;
      aspect-ratio: 1 / 1;
      width: 18vmin;
      height: 18vmin;
    }

    /* Responsive media queries for different PiP sizes */
    @media (max-width: 400px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 4vmin;
        padding: 0.8vmin 1.5vmin;
        border-radius: 1.2vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 5vmin;
        height: 5vmin;
      }

      .pip-tile-avatar {
        font-size: 4.5vmin;
        width: 15vmin;
        height: 15vmin;
      }
    }

    @media (max-width: 300px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 3.5vmin;
        padding: 0.6vmin 1.2vmin;
        border-radius: 1vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 4.5vmin;
        height: 4.5vmin;
      }

      .pip-tile-avatar {
        font-size: 4vmin;
        width: 12vmin;
        height: 12vmin;
      }
    }

    @media (max-width: 200px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 3vmin;
        padding: 0.5vmin 1vmin;
        border-radius: 0.8vmin;
        gap: 0.5vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 4vmin;
        height: 4vmin;
      }

      .pip-tile-avatar {
        font-size: 3.5vmin;
        width: 10vmin;
        height: 10vmin;
      }
    }

    @media (max-width: 100px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 2.5vmin;
        padding: 0.4vmin 0.8vmin;
        border-radius: 0.6vmin;
      }

      .pip-tile-overlay-left svg,
      .pip-tile-overlay-right svg {
        width: 3vmin;
        height: 3vmin;
      }

      .pip-tile-avatar {
        font-size: 3vmin;
        width: 8vmin;
        height: 8vmin;
      }
    }

    /* Height-based media queries */
    @media (max-height: 200px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 3vmin;
        padding: 0.5vmin 1vmin;
      }

      .pip-tile-avatar {
        font-size: 3.5vmin;
        width: 10vmin;
        height: 10vmin;
      }
    }

    @media (max-height: 100px) {
      .pip-tile-overlay-left,
      .pip-tile-overlay-right {
        font-size: 2.5vmin;
        padding: 0.4vmin 0.8vmin;
      }

      .pip-tile-avatar {
        font-size: 3vmin;
        width: 8vmin;
        height: 8vmin;
      }
    }

  `, []);

  // Simple error handling
  const handlePipError = useCallback(() => {
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document
      const pipDoc = pipWindow.document;
      const style = pipDoc.createElement('style');
      style.textContent = getPipStyles();
      pipDoc.head.appendChild(style);

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, getPipStyles, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported
  };
}